const express = require('express');
const session = require('express-session');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files with proper paths
app.use('/megacare/hqprd/assets', express.static(path.join(__dirname, 'public/assets')));
app.use('/megacare/hqprd/js', express.static(path.join(__dirname, 'public/js')));
app.use('/megacare/hqprd', express.static('public'));

// Session configuration
app.use(session({
  secret: 'megacare-hq-prd-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: { 
    secure: false, // Set to true if using HTTPS
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Database connection and initialization
const db = require('./config/database');
const initializeDatabase = require('./config/init-db');

// Initialize database
initializeDatabase();

// Routes
const authRoutes = require('./routes/auth');
const dashboardRoutes = require('./routes/dashboard');
const uploadRoutes = require('./routes/upload');
const apiRoutes = require('./routes/api');

app.use('/megacare/hqprd/auth', authRoutes);
app.use('/megacare/hqprd', dashboardRoutes);
app.use('/megacare/hqprd/upload', uploadRoutes);
app.use('/megacare/hqprd/api', apiRoutes);

// Root redirect
app.get('/', (req, res) => {
  res.redirect('/megacare/hqprd');
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Access the application at: http://localhost:${PORT}/megacare/hqprd`);
});
