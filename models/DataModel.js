const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

class DataModel {
  constructor() {
    this.models = new Map();
  }

  // Create or get a model for a specific month
  async getMonthlyModel(month) {
    const tableName = `data_${month.replace('-', '_')}`;

    if (this.models.has(tableName)) {
      return this.models.get(tableName);
    }

    // Get table structure from database
    try {
      const [tableInfo] = await sequelize.query(`
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = '${tableName}'
        ORDER BY ORDINAL_POSITION
      `);

      if (tableInfo.length === 0) {
        // Table doesn't exist, create basic model
        const MonthlyData = sequelize.define(tableName, {
          id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
          },
          month: {
            type: DataTypes.STRING(7),
            allowNull: false
          },
          uploadDate: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW
          },
          fileName: {
            type: DataTypes.STRING,
            allowNull: true
          }
        }, {
          tableName: tableName,
          timestamps: true
        });

        this.models.set(tableName, MonthlyData);
        return MonthlyData;
      }

      // Create model based on existing table structure
      const attributes = {};

      tableInfo.forEach(column => {
        const columnName = column.COLUMN_NAME;

        if (columnName === 'id') {
          attributes[columnName] = {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
          };
        } else if (columnName === 'createdAt' || columnName === 'updatedAt') {
          attributes[columnName] = {
            type: DataTypes.DATE,
            allowNull: false
          };
        } else {
          attributes[columnName] = {
            type: DataTypes.TEXT,
            allowNull: true
          };
        }
      });

      const MonthlyData = sequelize.define(tableName, attributes, {
        tableName: tableName,
        timestamps: true
      });

      this.models.set(tableName, MonthlyData);
      return MonthlyData;

    } catch (error) {
      console.error('Error getting monthly model:', error);
      throw error;
    }
  }

  // Create a model with dynamic columns based on Excel headers
  async createDynamicModel(month, headers) {
    const tableName = `data_${month.replace('-', '_')}`;
    
    // Define dynamic attributes
    const attributes = {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      month: {
        type: DataTypes.STRING(7),
        allowNull: false
      },
      uploadDate: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW
      },
      fileName: {
        type: DataTypes.STRING,
        allowNull: true
      }
    };

    // Add dynamic columns based on Excel headers
    headers.forEach(header => {
      const columnName = this.sanitizeColumnName(header);
      attributes[columnName] = {
        type: DataTypes.TEXT,
        allowNull: true
      };
    });

    // Create the model
    const DynamicModel = sequelize.define(tableName, attributes, {
      tableName: tableName,
      timestamps: true,
      indexes: [
        {
          fields: ['month']
        },
        {
          fields: ['uploadDate']
        }
      ]
    });

    // Store the model
    this.models.set(tableName, DynamicModel);

    // Create/update the table
    await DynamicModel.sync({ alter: true });

    return DynamicModel;
  }

  // Sanitize column names for database
  sanitizeColumnName(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_{2,}/g, '_')
      .replace(/^_|_$/g, '')
      .substring(0, 64); // MySQL column name limit
  }

  // Get all available months
  async getAvailableMonths() {
    try {
      const [results] = await sequelize.query(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME LIKE 'data_%'
        ORDER BY TABLE_NAME DESC
      `);

      return results.map(row => {
        const tableName = row.TABLE_NAME;
        const month = tableName.replace('data_', '').replace('_', '-');
        return month;
      });
    } catch (error) {
      console.error('Error getting available months:', error);
      return [];
    }
  }

  // Get data from multiple months within date range with pagination
  async getDataByDateRange(startDate, endDate, limit = null, offset = 0) {
    try {
      const availableMonths = await this.getAvailableMonths();
      let allData = [];

      for (const month of availableMonths) {
        const monthStart = new Date(month + '-01');
        const rangeStart = new Date(startDate);
        const rangeEnd = new Date(endDate);

        // Check if this month overlaps with the date range (more flexible)
        const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0);
        if ((monthStart >= rangeStart && monthStart <= rangeEnd) ||
            (monthEnd >= rangeStart && monthEnd <= rangeEnd) ||
            (rangeStart >= monthStart && rangeStart <= monthEnd)) {

          const model = await this.getMonthlyModel(month);

          // Build query options
          const queryOptions = {
            where: {
              month: month
            }
          };

          // Add pagination if limit is specified
          if (limit) {
            queryOptions.limit = parseInt(limit);
            queryOptions.offset = parseInt(offset);
          }

          const monthData = await model.findAll(queryOptions);

          // Convert to plain objects and add month info
          const plainData = monthData.map(record => {
            const data = record.toJSON();
            data.sourceMonth = month;
            return data;
          });

          allData = allData.concat(plainData);

          // If we have enough data and limit is set, break early
          if (limit && allData.length >= limit) {
            allData = allData.slice(0, limit);
            break;
          }
        }
      }

      return allData;
    } catch (error) {
      console.error('Error getting data by date range:', error);
      return [];
    }
  }

  // Get KPI data grouped by role
  async getKPIData(startDate, endDate) {
    try {
      const data = await this.getDataByDateRange(startDate, endDate);
      const kpi = {
        ZM: 0,
        RM: 0,
        HQ: 0,
        MR: 0
      };

      data.forEach(record => {
        // Look for role field in various possible column names
        const roleFields = ['role', 'designation', 'position', 'type'];
        let role = null;

        for (const field of roleFields) {
          if (record[field]) {
            role = record[field].toString().toUpperCase();
            break;
          }
        }

        if (role) {
          if (role.includes('ZM')) kpi.ZM++;
          else if (role.includes('RM')) kpi.RM++;
          else if (role.includes('HQ')) kpi.HQ++;
          else if (role.includes('MR')) kpi.MR++;
        }
      });

      return kpi;
    } catch (error) {
      console.error('Error getting KPI data:', error);
      return { ZM: 0, RM: 0, HQ: 0, MR: 0 };
    }
  }
}

module.exports = new DataModel();
