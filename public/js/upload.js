let selectedFile = null;

$(document).ready(function() {
    // Initialize month dropdown
    populateMonthDropdown();

    // Load upload history
    loadUploadHistory();

    // File upload area events
    const uploadArea = $('#uploadArea');
    const fileInput = $('#fileInput');

    console.log('Upload area found:', uploadArea.length);
    console.log('File input found:', fileInput.length);

    // Upload area click handler (only for drag area, not button)
    uploadArea.on('click', function(e) {
        // Don't trigger if clicking on browse button
        if (e.target.id !== 'browseBtn' && !$(e.target).closest('#browseBtn').length) {
            console.log('Upload area clicked');
            fileInput[0].click();
        }
    });

    // Browse button click handler - simple and direct
    $('#browseBtn').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Browse button clicked');
        document.getElementById('fileInput').click();
    });
    
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        uploadArea.addClass('dragover');
    });
    
    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        uploadArea.removeClass('dragover');
    });
    
    uploadArea.on('drop', function(e) {
        e.preventDefault();
        uploadArea.removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });
    
    fileInput.on('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
    
    // Form submission
    $('#uploadForm').on('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted');
        uploadFile();
    });

    // Upload button click handler
    $('#uploadBtn').on('click', function(e) {
        e.preventDefault();
        console.log('Upload button clicked');
        uploadFile();
    });
    
    // Custom month input handling
    $('#customMonth').on('input', function() {
        const customMonth = $(this).val();
        if (customMonth) {
            $('#monthSelect').val('');
        }
    });
    
    $('#monthSelect').on('change', function() {
        if ($(this).val()) {
            $('#customMonth').val('');
        }
    });
});

function populateMonthDropdown() {
    const monthSelect = $('#monthSelect');
    const now = new Date();
    
    // Current month
    const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
    
    // Previous month
    const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const previousMonth = prevMonth.getFullYear() + '-' + String(prevMonth.getMonth() + 1).padStart(2, '0');
    
    // Next month
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    const nextMonthStr = nextMonth.getFullYear() + '-' + String(nextMonth.getMonth() + 1).padStart(2, '0');
    
    monthSelect.append(`<option value="">Select Month</option>`);
    monthSelect.append(`<option value="${previousMonth}">${formatMonthDisplay(previousMonth)} (Previous)</option>`);
    monthSelect.append(`<option value="${currentMonth}" selected>${formatMonthDisplay(currentMonth)} (Current)</option>`);
    monthSelect.append(`<option value="${nextMonthStr}">${formatMonthDisplay(nextMonthStr)} (Next)</option>`);
}

function formatMonthDisplay(monthStr) {
    const [year, month] = monthStr.split('-');
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
}

function handleFileSelect(file) {
    // Validate file type
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/csv',
        'text/plain'
    ];

    const allowedExtensions = ['.csv', '.xlsx', '.xls'];
    const fileName = file.name.toLowerCase();
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

    if (!allowedTypes.includes(file.type) && !hasValidExtension) {
        showNotification('Please select a valid file (.csv, .xlsx, or .xls)', 'danger');
        return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        showNotification('File size too large. Maximum allowed size is 10MB.', 'danger');
        return;
    }

    // Additional validation for CSV files
    if (fileName.endsWith('.csv')) {
        // Check if file is too small (likely empty)
        if (file.size < 10) {
            showNotification('CSV file appears to be empty or too small.', 'danger');
            return;
        }

        // Check if file is suspiciously large for a CSV
        if (file.size > 5 * 1024 * 1024) { // 5MB
            if (!confirm('This CSV file is quite large (>5MB). It may take longer to process. Continue?')) {
                return;
            }
        }
    }
    
    selectedFile = file;
    
    // Show file information
    $('#fileName').text(file.name);
    $('#fileSize').text(formatFileSize(file.size));
    $('#fileInfo').show();
    
    // Enable upload button
    $('#uploadBtn').prop('disabled', false);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function uploadFile() {
    console.log('uploadFile function called');
    console.log('selectedFile:', selectedFile);

    if (!selectedFile) {
        showNotification('Please select a file to upload', 'warning');
        return;
    }

    const month = $('#monthSelect').val() || $('#customMonth').val();
    console.log('Selected month:', month);

    if (!month) {
        showNotification('Please select or enter a month', 'warning');
        return;
    }

    // Validate custom month format
    if ($('#customMonth').val() && !/^\d{4}-\d{2}$/.test($('#customMonth').val())) {
        showNotification('Please enter month in YYYY-MM format', 'warning');
        return;
    }

    console.log('Starting file upload...');
    
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('month', month);
    
    // Show progress bar
    $('#uploadProgress').show();
    $('#uploadBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Uploading...');
    
    $.ajax({
        url: '/megacare/hqprd/upload/file',
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    $('.progress-bar').css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function(response) {
            console.log('Upload response:', response);
            if (response.success) {
                showNotification(`File uploaded successfully! ${response.recordsProcessed} records processed.`, 'success');
                resetForm();
                loadUploadHistory();

                // Show option to view dashboard
                setTimeout(() => {
                    if (confirm('File uploaded successfully! Would you like to view the dashboard?')) {
                        window.location.href = '/megacare/hqprd/dashboard';
                    }
                }, 1000);
            } else {
                showNotification(response.message || 'Upload failed', 'danger');
            }
        },
        error: function(xhr) {
            const response = xhr.responseJSON;
            showNotification(response?.message || 'Upload failed', 'danger');
        },
        complete: function() {
            $('#uploadProgress').hide();
            $('.progress-bar').css('width', '0%');
            $('#uploadBtn').prop('disabled', false).html('<i class="nc-icon nc-cloud-upload-94"></i> Upload File');
        }
    });
}

function resetForm() {
    selectedFile = null;
    $('#fileInput').val('');
    $('#fileInfo').hide();
    $('#uploadBtn').prop('disabled', true);
    $('#customMonth').val('');
    
    // Reset to current month
    const now = new Date();
    const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
    $('#monthSelect').val(currentMonth);
}

function loadUploadHistory() {
    $.ajax({
        url: '/megacare/hqprd/api/upload-history',
        method: 'GET',
        success: function(response) {
            const tbody = $('#historyTable tbody');
            tbody.empty();
            
            if (response.history && response.history.length > 0) {
                response.history.forEach(item => {
                    const row = `
                        <tr>
                            <td>${new Date(item.uploadDate).toLocaleDateString()}</td>
                            <td>${formatMonthDisplay(item.month)}</td>
                            <td>${item.fileName}</td>
                            <td>${item.recordCount}</td>
                            <td><span class="badge badge-success">Success</span></td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            } else {
                tbody.append('<tr><td colspan="5" class="text-center">No upload history found</td></tr>');
            }
        },
        error: function(xhr) {
            console.error('Error loading upload history:', xhr);
        }
    });
}

function logout() {
    // Create a form and submit it for POST logout
    const form = $('<form>', {
        'method': 'POST',
        'action': '/megacare/hqprd/logout'
    });
    $('body').append(form);
    form.submit();
}

function showNotification(message, type = 'info') {
    const notification = $(`
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `);
    
    $('body').append(notification);
    
    setTimeout(() => {
        notification.alert('close');
    }, 5000);
}
