let dataTable = null;
let currentData = [];

$(document).ready(function() {
    // Initialize dashboard
    loadDashboardData();
    
    // Set default date range (current month)
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    $('#startDate').val(formatDate(firstDay));
    $('#endDate').val(formatDate(lastDay));
    
    // Tab change event
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        const target = $(e.target).attr("href");
        const role = target.substring(1); // Remove # from target
        loadTableData(role);
    });
});

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function loadDashboardData() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    
    $.ajax({
        url: '/megacare/hqprd/api/dashboard-data',
        method: 'GET',
        data: { startDate, endDate },
        success: function(response) {
            updateKPICards(response.kpi);
            currentData = response.data;
            loadTableData('all');
        },
        error: function(xhr) {
            console.error('Error loading dashboard data:', xhr);
            showNotification('Error loading dashboard data', 'danger');
        }
    });
}

function updateKPICards(kpi) {
    $('#zmCount').text(kpi.ZM || 0);
    $('#rmCount').text(kpi.RM || 0);
    $('#hqCount').text(kpi.HQ || 0);
    $('#mrCount').text(kpi.MR || 0);
}

function loadTableData(role) {
    let tableId = role === 'all' ? '#allDataTable' : `#${role}DataTable`;
    let filteredData = currentData;
    
    // Filter data by role if not 'all'
    if (role !== 'all') {
        filteredData = currentData.filter(item => 
            item.role && item.role.toUpperCase() === role.toUpperCase()
        );
    }
    
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable(tableId)) {
        $(tableId).DataTable().destroy();
    }
    
    // Clear table
    $(tableId + ' thead').empty();
    $(tableId + ' tbody').empty();
    
    if (filteredData.length > 0) {
        // Create headers dynamically
        const headers = Object.keys(filteredData[0]);
        let headerHtml = '<tr>';
        headers.forEach(header => {
            headerHtml += `<th>${header.charAt(0).toUpperCase() + header.slice(1)}</th>`;
        });
        headerHtml += '</tr>';
        $(tableId + ' thead').html(headerHtml);
        
        // Create rows
        let bodyHtml = '';
        filteredData.forEach(row => {
            bodyHtml += '<tr>';
            headers.forEach(header => {
                bodyHtml += `<td>${row[header] || ''}</td>`;
            });
            bodyHtml += '</tr>';
        });
        $(tableId + ' tbody').html(bodyHtml);
        
        // Initialize DataTable with pagination options
        $(tableId).DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 20, 50, 100, -1], [10, 20, 50, 100, "All"]],
            "responsive": true,
            "scrollX": true,
            "order": [],
            "language": {
                "search": "Search:",
                "lengthMenu": "Show _MENU_ entries",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    } else {
        $(tableId + ' thead').html('<tr><th>No Data Available</th></tr>');
        $(tableId + ' tbody').html('<tr><td>No data found for the selected criteria.</td></tr>');
    }
}

function filterData() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    
    if (!startDate || !endDate) {
        showNotification('Please select both start and end dates', 'warning');
        return;
    }
    
    if (new Date(startDate) > new Date(endDate)) {
        showNotification('Start date cannot be greater than end date', 'warning');
        return;
    }
    
    loadDashboardData();
}

function logout() {
    // Create a form and submit it for POST logout
    const form = $('<form>', {
        'method': 'POST',
        'action': '/megacare/hqprd/logout'
    });
    $('body').append(form);
    form.submit();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = $(`
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `);
    
    $('body').append(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.alert('close');
    }, 5000);
}
