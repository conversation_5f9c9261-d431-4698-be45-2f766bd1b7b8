<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="/megacare/hqprd/assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="/megacare/hqprd/assets/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title>Megacare HQ PRD - Upload Data</title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  <link href="/megacare/hqprd/assets/css/bootstrap.min.css" rel="stylesheet" />
  <link href="/megacare/hqprd/assets/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  <link href="/megacare/hqprd/assets/demo/demo.css" rel="stylesheet" />
  <style>
    .upload-area {
      border: 2px dashed #ccc;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      transition: border-color 0.3s ease;
      cursor: pointer;
    }
    .upload-area:hover {
      border-color: #007bff;
    }
    .upload-area.dragover {
      border-color: #007bff;
      background-color: #f8f9fa;
    }
    .file-info {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 5px;
      display: none;
    }
    .progress {
      margin-top: 20px;
      display: none;
    }
  </style>
</head>

<body class="">
  <div class="wrapper">
    <div class="sidebar" data-color="white" data-active-color="danger">
      <div class="logo">
        <a href="#" class="simple-text logo-mini">
          <div class="logo-image-small">
            <img src="/megacare/hqprd/assets/img/logo-small.png">
          </div>
        </a>
        <a href="#" class="simple-text logo-normal">
          Megacare HQ PRD
        </a>
      </div>
      <div class="sidebar-wrapper">
        <ul class="nav">
          <li>
            <a href="/megacare/hqprd/dashboard">
              <i class="nc-icon nc-bank"></i>
              <p>Dashboard</p>
            </a>
          </li>
          <li class="active">
            <a href="/megacare/hqprd/upload">
              <i class="nc-icon nc-cloud-upload-94"></i>
              <p>Upload Data</p>
            </a>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="main-panel">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
        <div class="container-fluid">
          <div class="navbar-wrapper">
            <div class="navbar-toggle">
              <button type="button" class="navbar-toggler">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
              </button>
            </div>
            <a class="navbar-brand" href="javascript:;">Upload Data</a>
          </div>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation">
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
          </button>
          <div class="collapse navbar-collapse justify-content-end" id="navigation">
            <ul class="navbar-nav">
              <li class="nav-item">
                <a class="nav-link btn-rotate" href="javascript:;" onclick="logout()">
                  <i class="nc-icon nc-button-power"></i>
                  <p><span class="d-lg-none d-md-block">Logout</span></p>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <!-- End Navbar -->
      
      <div class="content">
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Upload Excel File</h4>
                <p class="card-category">Select month and upload your Excel file</p>
              </div>
              <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                  <!-- Month Selection -->
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="monthSelect">Select Month</label>
                        <select class="form-control" id="monthSelect" name="month" required>
                          <!-- Options will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="customMonth">Or Enter Custom Month (YYYY-MM)</label>
                        <input type="text" class="form-control" id="customMonth" name="customMonth" 
                               placeholder="e.g., 2025-01" pattern="[0-9]{4}-[0-9]{2}">
                        <small class="form-text text-muted">Format: YYYY-MM (e.g., 2025-01)</small>
                      </div>
                    </div>
                  </div>
                  
                  <!-- File Upload Area -->
                  <div class="upload-area" id="uploadArea">
                    <i class="nc-icon nc-cloud-upload-94" style="font-size: 48px; color: #ccc;"></i>
                    <h4>Drag & Drop Excel File Here</h4>
                    <p>or <strong>click to browse</strong></p>
                    <p class="text-muted">Supported formats: .xlsx, .xls</p>
                    <input type="file" id="fileInput" name="file" accept=".xlsx,.xls" style="display: none;" required>
                  </div>
                  
                  <!-- File Information -->
                  <div class="file-info" id="fileInfo">
                    <h5>Selected File:</h5>
                    <p id="fileName"></p>
                    <p id="fileSize"></p>
                  </div>
                  
                  <!-- Progress Bar -->
                  <div class="progress" id="uploadProgress">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                  </div>
                  
                  <!-- Upload Button -->
                  <div class="text-center" style="margin-top: 20px;">
                    <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn" disabled>
                      <i class="nc-icon nc-cloud-upload-94"></i> Upload File
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Upload History -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Upload History</h4>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped" id="historyTable">
                    <thead class="text-primary">
                      <tr>
                        <th>Date</th>
                        <th>Month</th>
                        <th>File Name</th>
                        <th>Records</th>
                        <th>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <!-- History will be loaded here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <footer class="footer footer-black footer-white">
        <div class="container-fluid">
          <div class="row">
            <div class="credits ml-auto">
              <span class="copyright">
                © <script>document.write(new Date().getFullYear())</script>, Megacare HQ PRD
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="/megacare/hqprd/assets/js/core/jquery.min.js"></script>
  <script src="/megacare/hqprd/assets/js/core/popper.min.js"></script>
  <script src="/megacare/hqprd/assets/js/core/bootstrap.min.js"></script>
  <script src="/megacare/hqprd/assets/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="/megacare/hqprd/assets/js/paper-dashboard.min.js?v=2.0.1"></script>

  <script src="/megacare/hqprd/js/upload.js"></script>
</body>
</html>
