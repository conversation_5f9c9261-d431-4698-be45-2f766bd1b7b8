<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="/megacare/hqprd/assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="/megacare/hqprd/assets/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title>Megacare HQ PRD - Login</title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  <link href="/megacare/hqprd/assets/css/bootstrap.min.css" rel="stylesheet" />
  <link href="/megacare/hqprd/assets/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  <style>
    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .login-card {
      width: 100%;
      max-width: 400px;
      padding: 2rem;
      background: white;
      border-radius: 10px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }
    .login-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    .login-header h2 {
      color: #333;
      margin-bottom: 0.5rem;
    }
    .login-header p {
      color: #666;
      margin: 0;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h2>Megacare HQ PRD</h2>
        <p>Please sign in to continue</p>
      </div>
      
      <form id="loginForm" method="POST" action="/megacare/hqprd/login">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" class="form-control" id="username" name="username" required>
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" class="form-control" id="password" name="password" required>
        </div>

        <div id="errorMessage" class="alert alert-danger" style="display: none;"></div>

        <button type="submit" class="btn btn-primary btn-block">Sign In</button>
      </form>
    </div>
  </div>

  <script src="/megacare/hqprd/assets/js/core/jquery.min.js"></script>
  <script src="/megacare/hqprd/assets/js/core/bootstrap.min.js"></script>
  <script>
    $(document).ready(function() {
      // Check for error parameter in URL
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('error') === 'invalid') {
        $('#errorMessage').text('Invalid username or password').show();
      }

      // Form validation
      $('#loginForm').on('submit', function(e) {
        const username = $('#username').val().trim();
        const password = $('#password').val().trim();

        if (!username || !password) {
          e.preventDefault();
          $('#errorMessage').text('Please enter both username and password').show();
          return false;
        }

        // Show loading state
        $('button[type="submit"]').html('<i class="fa fa-spinner fa-spin"></i> Signing In...').prop('disabled', true);
      });
    });
  </script>
</body>
</html>
