<!DOCTYPE html>
<html>
<head>
    <title>Test File Upload</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test File Upload</h1>
    
    <div style="border: 2px dashed #ccc; padding: 20px; margin: 20px; cursor: pointer;" id="testUploadArea">
        <p>Click here to select file</p>
        <input type="file" id="testFileInput" accept=".csv,.xlsx,.xls" style="display: none;">
    </div>
    
    <div id="fileInfo" style="margin: 20px; display: none;">
        <p>Selected file: <span id="fileName"></span></p>
        <p>File size: <span id="fileSize"></span></p>
    </div>
    
    <script>
        $(document).ready(function() {
            console.log('Test page loaded');
            
            $('#testUploadArea').on('click', function() {
                console.log('Test area clicked');
                $('#testFileInput')[0].click();
            });
            
            $('#testFileInput').on('change', function(e) {
                console.log('File selected');
                const file = e.target.files[0];
                if (file) {
                    $('#fileName').text(file.name);
                    $('#fileSize').text((file.size / 1024).toFixed(2) + ' KB');
                    $('#fileInfo').show();
                }
            });
        });
    </script>
</body>
</html>
