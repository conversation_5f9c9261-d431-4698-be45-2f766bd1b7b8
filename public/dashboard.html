<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="apple-touch-icon" sizes="76x76" href="/megacare/hqprd/assets/img/apple-icon.png">
  <link rel="icon" type="image/png" href="/megacare/hqprd/assets/img/favicon.png">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <title>Megacare HQ PRD - Dashboard</title>
  <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />
  <link href="https://fonts.googleapis.com/css?family=Montserrat:400,700,200" rel="stylesheet" />
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
  <link href="/megacare/hqprd/assets/css/bootstrap.min.css" rel="stylesheet" />
  <link href="/megacare/hqprd/assets/css/paper-dashboard.css?v=2.0.1" rel="stylesheet" />
  <link href="/megacare/hqprd/assets/demo/demo.css" rel="stylesheet" />
  <!-- DataTables CSS -->
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap4.min.css">
</head>

<body class="">
  <div class="wrapper">
    <div class="sidebar" data-color="white" data-active-color="danger">
      <div class="logo">
        <a href="#" class="simple-text logo-mini">
          <div class="logo-image-small">
            <img src="/megacare/hqprd/assets/img/logo-small.png">
          </div>
        </a>
        <a href="#" class="simple-text logo-normal">
          Megacare HQ PRD
        </a>
      </div>
      <div class="sidebar-wrapper">
        <ul class="nav">
          <li class="active">
            <a href="/megacare/hqprd/dashboard">
              <i class="nc-icon nc-bank"></i>
              <p>Dashboard</p>
            </a>
          </li>
          <li>
            <a href="/megacare/hqprd/upload">
              <i class="nc-icon nc-cloud-upload-94"></i>
              <p>Upload Data</p>
            </a>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="main-panel">
      <!-- Navbar -->
      <nav class="navbar navbar-expand-lg navbar-absolute fixed-top navbar-transparent">
        <div class="container-fluid">
          <div class="navbar-wrapper">
            <div class="navbar-toggle">
              <button type="button" class="navbar-toggler">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
              </button>
            </div>
            <a class="navbar-brand" href="javascript:;">Dashboard</a>
          </div>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navigation">
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
            <span class="navbar-toggler-bar navbar-kebab"></span>
          </button>
          <div class="collapse navbar-collapse justify-content-end" id="navigation">
            <ul class="navbar-nav">
              <li class="nav-item">
                <a class="nav-link btn-rotate" href="javascript:;" onclick="logout()">
                  <i class="nc-icon nc-button-power"></i>
                  <p><span class="d-lg-none d-md-block">Logout</span></p>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <!-- End Navbar -->
      
      <div class="content">
        <!-- Date Range Filter -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title">Date Range Filter</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label>Start Date</label>
                      <input type="date" class="form-control" id="startDate">
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label>End Date</label>
                      <input type="date" class="form-control" id="endDate">
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label>&nbsp;</label>
                      <button class="btn btn-primary btn-block" onclick="filterData()">Filter Data</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- KPI Cards -->
        <div class="row" id="kpiCards">
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-single-02 text-warning"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">ZM</p>
                      <p class="card-title" id="zmCount">0</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-circle-10 text-success"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">RM</p>
                      <p class="card-title" id="rmCount">0</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-diamond text-danger"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">HQ</p>
                      <p class="card-title" id="hqCount">0</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
              <div class="card-body">
                <div class="row">
                  <div class="col-5 col-md-4">
                    <div class="icon-big text-center icon-warning">
                      <i class="nc-icon nc-favourite-28 text-primary"></i>
                    </div>
                  </div>
                  <div class="col-7 col-md-8">
                    <div class="numbers">
                      <p class="card-category">MR</p>
                      <p class="card-title" id="mrCount">0</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Data Table with Tabs -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Data Overview</h4>
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="dataTab" role="tablist">
                  <li class="nav-item">
                    <a class="nav-link active" id="all-tab" data-toggle="tab" href="#all" role="tab">All Data</a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="zm-tab" data-toggle="tab" href="#zm" role="tab">ZM</a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="rm-tab" data-toggle="tab" href="#rm" role="tab">RM</a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="hq-tab" data-toggle="tab" href="#hq" role="tab">HQ</a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" id="mr-tab" data-toggle="tab" href="#mr" role="tab">MR</a>
                  </li>
                </ul>
              </div>
              <div class="card-body">
                <div class="tab-content" id="dataTabContent">
                  <div class="tab-pane fade show active" id="all" role="tabpanel">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                      <table class="table table-striped table-hover" id="allDataTable" style="width: 100%;">
                        <thead class="text-primary" style="position: sticky; top: 0; background: white; z-index: 10;">
                          <!-- Dynamic headers will be loaded here -->
                        </thead>
                        <tbody>
                          <!-- Dynamic data will be loaded here -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div class="tab-pane fade" id="zm" role="tabpanel">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                      <table class="table table-striped table-hover" id="zmDataTable" style="width: 100%;">
                        <thead class="text-primary" style="position: sticky; top: 0; background: white; z-index: 10;">
                          <!-- Dynamic headers will be loaded here -->
                        </thead>
                        <tbody>
                          <!-- Dynamic data will be loaded here -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div class="tab-pane fade" id="rm" role="tabpanel">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                      <table class="table table-striped table-hover" id="rmDataTable" style="width: 100%;">
                        <thead class="text-primary" style="position: sticky; top: 0; background: white; z-index: 10;">
                          <!-- Dynamic headers will be loaded here -->
                        </thead>
                        <tbody>
                          <!-- Dynamic data will be loaded here -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div class="tab-pane fade" id="hq" role="tabpanel">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                      <table class="table table-striped table-hover" id="hqDataTable" style="width: 100%;">
                        <thead class="text-primary" style="position: sticky; top: 0; background: white; z-index: 10;">
                          <!-- Dynamic headers will be loaded here -->
                        </thead>
                        <tbody>
                          <!-- Dynamic data will be loaded here -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div class="tab-pane fade" id="mr" role="tabpanel">
                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                      <table class="table table-striped table-hover" id="mrDataTable" style="width: 100%;">
                        <thead class="text-primary" style="position: sticky; top: 0; background: white; z-index: 10;">
                          <!-- Dynamic headers will be loaded here -->
                        </thead>
                        <tbody>
                          <!-- Dynamic data will be loaded here -->
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <footer class="footer footer-black footer-white">
        <div class="container-fluid">
          <div class="row">
            <div class="credits ml-auto">
              <span class="copyright">
                © <script>document.write(new Date().getFullYear())</script>, Megacare HQ PRD
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>

  <!-- Core JS Files -->
  <script src="/megacare/hqprd/assets/js/core/jquery.min.js"></script>
  <script src="/megacare/hqprd/assets/js/core/popper.min.js"></script>
  <script src="/megacare/hqprd/assets/js/core/bootstrap.min.js"></script>
  <script src="/megacare/hqprd/assets/js/plugins/perfect-scrollbar.jquery.min.js"></script>
  <script src="/megacare/hqprd/assets/js/paper-dashboard.min.js?v=2.0.1"></script>
  
  <!-- DataTables JS -->
  <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
  <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap4.min.js"></script>

  <script src="/megacare/hqprd/js/dashboard.js"></script>
</body>
</html>
