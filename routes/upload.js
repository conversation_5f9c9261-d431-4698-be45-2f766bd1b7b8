const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const XLSX = require('xlsx');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const DataModel = require('../models/DataModel');

// Intelligent CSV parsing function
async function parseCSVIntelligently(csvContent) {
  console.log('Starting intelligent CSV parsing...');

  // First, try standard line-based parsing
  const lines = csvContent.split(/\r?\n/).filter(line => line.trim() !== '');
  console.log('Lines found:', lines.length);

  if (lines.length === 0) {
    throw new Error('CSV file is empty');
  }

  // If we have multiple lines, use standard parsing
  if (lines.length > 1) {
    console.log('Using standard multi-line parsing');
    return lines.map(line => {
      return line.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
    });
  }

  // If only one line, try to intelligently split it
  console.log('Single line detected, attempting intelligent parsing...');
  const singleLine = lines[0];

  // Look for patterns that might indicate where headers end and data begins
  const parts = singleLine.split(',').map(col => col.trim().replace(/^"|"$/g, ''));
  console.log('Total parts found:', parts.length);

  // Try to identify a reasonable header pattern
  // Look for the first occurrence of what looks like data vs headers
  let headerEndIndex = -1;

  // Common header patterns in your data
  const commonHeaders = [
    'DIVISION_CODE', 'DIVISION_NAME', 'ZM', 'ZM_NAME', 'RM', 'RM_NAME',
    'AM', 'AM_NAME', 'MR_CURRENT', 'MR_NAME', 'HQ_NAME', 'POOL_CODE',
    'POOL_NAME', 'MR_COUNT_04', 'MR_COUNT_05', 'HQ_COUNT_04', 'HQ_COUNT_05',
    'PHYZII_DISHA', 'MATERIAL', 'MATERIAL_DESCRIPTION', 'PRODUCT_COMMON_CODE',
    'PRODUCT_COMMON_CODE_DES', 'PRODUCT_GROUP', 'PRODUCT_GROUP2', 'PRODUCT_GROUP3',
    'LY_04_NET_QTY', 'LY_05_NET_QTY', 'LY_06_NET_QTY', 'LY_07_NET_QTY',
    'LY_08_NET_QTY', 'LY_09_NET_QTY', 'LY_10_NET_QTY', 'LY_11_NET_QTY',
    'LY_12_NET_QTY', 'LY_01_NET_QTY', 'LY_02_NET_QTY', 'LY_03_NET_QTY',
    'LY_04_NET_VALUE', 'LY_05_NET_VALUE', 'LY_06_NET_VALUE', 'LY_07_NET_VALUE',
    'LY_08_NET_VALUE', 'LY_09_NET_VALUE', 'LY_10_NET_VALUE', 'LY_11_NET_VALUE',
    'LY_12_NET_VALUE', 'LY_01_NET_VALUE', 'LY_02_NET_VALUE', 'LY_03_NET_VALUE',
    'CY_04_TRG_QTY', 'CY_05_TRG_QTY', 'CY_06_TRG_QTY', 'CY_04_TRG_VALUE',
    'CY_05_TRG_VALUE', 'CY_06_TRG_VALUE', 'CY_04_NET_QTY', 'CY_05_NET_QTY',
    'CY_04_NET_VALUE', 'CY_05_NET_VALUE', 'LY_YTD_NET_QTY', 'LY_TOTAL_NET_QTY',
    'CY_YTD_TRGQTY', 'CY_YTD_NET_QTY', 'LY_YTD_NET_VALUE', 'LY_TOTAL_NET_VALUE',
    'CY_YTD_TRGVAL', 'CY_YTD_NET_VALUE'
  ];

  // Find where headers likely end (around 60-70 columns based on your data)
  headerEndIndex = Math.min(commonHeaders.length, 64); // Your data has 64 headers

  console.log('Estimated header end index:', headerEndIndex);

  if (headerEndIndex > 0 && headerEndIndex < parts.length) {
    const headers = parts.slice(0, headerEndIndex);
    const remainingData = parts.slice(headerEndIndex);

    console.log('Headers extracted:', headers.length);
    console.log('Remaining data parts:', remainingData.length);

    // Try to chunk remaining data into rows based on header count
    const rows = [headers];
    const chunkSize = headers.length;

    for (let i = 0; i < remainingData.length; i += chunkSize) {
      const chunk = remainingData.slice(i, i + chunkSize);
      if (chunk.length === chunkSize) {
        rows.push(chunk);
      }
    }

    console.log('Reconstructed rows:', rows.length);
    return rows;
  }

  // Fallback: if we can't parse intelligently, throw a helpful error
  throw new Error(`Unable to parse CSV file. The file appears to have ${parts.length} columns in a single line, which suggests a formatting issue. Please ensure your CSV file has proper line breaks between rows.`);
}
const UploadHistory = require('../models/UploadHistory');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  // No file size limits
  fileFilter: function (req, file, cb) {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/csv',
      'text/plain'
    ];

    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel files (.xlsx, .xls) and CSV files (.csv) are allowed'));
    }
  }
});

// File upload endpoint
router.post('/file', isAuthenticated, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    const { month } = req.body;
    if (!month) {
      // Clean up uploaded file
      fs.unlinkSync(req.file.path);
      return res.status(400).json({ success: false, message: 'Month is required' });
    }

    // Validate month format
    if (!/^\d{4}-\d{2}$/.test(month)) {
      fs.unlinkSync(req.file.path);
      return res.status(400).json({ success: false, message: 'Invalid month format. Use YYYY-MM' });
    }

    // Create upload history record
    const uploadRecord = await UploadHistory.create({
      month: month,
      fileName: req.file.filename,
      originalName: req.file.originalname,
      fileSize: req.file.size,
      uploadedBy: req.session.user.username,
      status: 'processing'
    });

    try {
      let jsonData = [];
      const fileExtension = path.extname(req.file.originalname).toLowerCase();

      console.log('Processing file:', req.file.originalname);
      console.log('File size:', req.file.size, 'bytes');
      console.log('File extension:', fileExtension);

      if (fileExtension === '.csv') {
        // Process CSV file with intelligent parsing
        const fs = require('fs');
        const csvContent = fs.readFileSync(req.file.path, 'utf8');

        console.log('CSV file size:', csvContent.length, 'characters');
        console.log('First 200 characters:', csvContent.substring(0, 200));

        // Try to intelligently parse the CSV
        jsonData = await parseCSVIntelligently(csvContent);

        console.log('CSV parsed successfully, total rows:', jsonData.length);
        console.log('First row column count:', jsonData[0] ? jsonData[0].length : 0);

      } else {
        // Process Excel file
        const workbook = XLSX.readFile(req.file.path);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON
        jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      }

      if (jsonData.length === 0) {
        throw new Error('File is empty');
      }

      // Get headers from first row
      const rawHeaders = jsonData[0];
      const dataRows = jsonData.slice(1);

      console.log('Raw headers sample (first 10):', rawHeaders ? rawHeaders.slice(0, 10) : 'No headers');
      console.log('Number of raw columns:', rawHeaders ? rawHeaders.length : 0);

      // Early check for abnormal column count - but allow intelligent parser to handle it
      if (!rawHeaders) {
        throw new Error('No headers found in CSV file');
      }

      // If we have too many columns, it means the intelligent parser couldn't fix the format
      if (rawHeaders.length > 1000) {
        throw new Error(`CSV file format issue detected. The file appears to have ${rawHeaders.length} columns, which suggests the data is not properly structured with line breaks. Please re-save your CSV file ensuring each row is on a separate line.`);
      }

      // Filter and validate headers - ensure they are not empty and not just numbers
      const validHeaders = rawHeaders.filter((header, index) => {
        if (!header || header.trim() === '') {
          if (index < 10) console.log(`Skipping empty header at index ${index}`);
          return false;
        }

        // Skip if header is just a number (likely an index)
        if (/^\d+$/.test(header.toString().trim())) {
          if (index < 10) console.log(`Skipping numeric header: ${header} at index ${index}`);
          return false;
        }

        return true;
      });

      console.log('Valid headers sample (first 10):', validHeaders.slice(0, 10));
      console.log('Valid headers count:', validHeaders.length);

      // Validate column count (MySQL limit is around 4096 columns, but we'll be conservative)
      if (validHeaders.length > 200) {
        throw new Error(`Too many valid columns (${validHeaders.length}). Maximum allowed is 200 columns. Please reduce the number of columns in your CSV file.`);
      }

      if (validHeaders.length === 0) {
        throw new Error('No valid column headers found in CSV file. Please ensure first row contains proper column names (not numbers or empty values).');
      }

      // Create dynamic model based on valid headers only
      const model = await DataModel.createDynamicModel(month, validHeaders);

      // Prepare data for insertion
      const recordsToInsert = [];
      
      dataRows.forEach(row => {
        if (row.some(cell => cell !== null && cell !== undefined && cell !== '')) {
          const record = {
            month: month,
            fileName: req.file.originalname,
            uploadDate: new Date()
          };

          // Map row data to sanitized column names (only valid headers)
          // Find the original index of each valid header in the raw headers
          validHeaders.forEach((header) => {
            const originalIndex = rawHeaders.indexOf(header);
            if (originalIndex !== -1 && header && header.trim() !== '') {
              const columnName = DataModel.sanitizeColumnName(header);
              record[columnName] = row[originalIndex] || null;
            }
          });

          recordsToInsert.push(record);
        }
      });

      // Insert data in batches
      const batchSize = 1000;
      let totalInserted = 0;

      for (let i = 0; i < recordsToInsert.length; i += batchSize) {
        const batch = recordsToInsert.slice(i, i + batchSize);
        await model.bulkCreate(batch);
        totalInserted += batch.length;
      }

      // Update upload history
      await uploadRecord.update({
        recordCount: totalInserted,
        status: 'success'
      });

      // Clean up uploaded file
      fs.unlinkSync(req.file.path);

      res.json({
        success: true,
        message: 'File uploaded and processed successfully',
        recordsProcessed: totalInserted,
        month: month
      });

    } catch (processingError) {
      console.error('Error processing file:', processingError);
      
      // Update upload history with error
      await uploadRecord.update({
        status: 'failed',
        errorMessage: processingError.message
      });

      // Clean up uploaded file
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        message: 'Error processing file: ' + processingError.message
      });
    }

  } catch (error) {
    console.error('Upload error:', error);
    
    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Upload failed'
    });
  }
});

// Get upload history
router.get('/history', isAuthenticated, async (req, res) => {
  try {
    const history = await UploadHistory.findAll({
      order: [['uploadDate', 'DESC']],
      limit: 50
    });

    res.json({
      success: true,
      history: history
    });
  } catch (error) {
    console.error('Error fetching upload history:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching upload history'
    });
  }
});

module.exports = router;
