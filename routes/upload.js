const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const XLSX = require('xlsx');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const DataModel = require('../models/DataModel');
const UploadHistory = require('../models/UploadHistory');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/csv',
      'text/plain'
    ];

    const allowedExtensions = ['.xlsx', '.xls', '.csv'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel files (.xlsx, .xls) and CSV files (.csv) are allowed'));
    }
  }
});

// File upload endpoint
router.post('/file', isAuthenticated, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    const { month } = req.body;
    if (!month) {
      // Clean up uploaded file
      fs.unlinkSync(req.file.path);
      return res.status(400).json({ success: false, message: 'Month is required' });
    }

    // Validate month format
    if (!/^\d{4}-\d{2}$/.test(month)) {
      fs.unlinkSync(req.file.path);
      return res.status(400).json({ success: false, message: 'Invalid month format. Use YYYY-MM' });
    }

    // Create upload history record
    const uploadRecord = await UploadHistory.create({
      month: month,
      fileName: req.file.filename,
      originalName: req.file.originalname,
      fileSize: req.file.size,
      uploadedBy: req.session.user.username,
      status: 'processing'
    });

    try {
      let jsonData = [];
      const fileExtension = path.extname(req.file.originalname).toLowerCase();

      if (fileExtension === '.csv') {
        // Process CSV file
        const fs = require('fs');
        const csvContent = fs.readFileSync(req.file.path, 'utf8');

        // Parse CSV properly
        const lines = csvContent.split('\n').filter(line => line.trim() !== '');
        if (lines.length === 0) {
          throw new Error('CSV file is empty');
        }

        // Convert CSV to array format with proper parsing
        jsonData = lines.map(line => {
          const result = [];
          let current = '';
          let inQuotes = false;

          for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
              inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
              result.push(current.trim());
              current = '';
            } else {
              current += char;
            }
          }
          result.push(current.trim());

          return result;
        });

      } else {
        // Process Excel file
        const workbook = XLSX.readFile(req.file.path);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON
        jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      }

      if (jsonData.length === 0) {
        throw new Error('File is empty');
      }

      // Get headers from first row
      const headers = jsonData[0];
      const dataRows = jsonData.slice(1);

      // Create dynamic model based on headers
      const model = await DataModel.createDynamicModel(month, headers);

      // Prepare data for insertion
      const recordsToInsert = [];
      
      dataRows.forEach(row => {
        if (row.some(cell => cell !== null && cell !== undefined && cell !== '')) {
          const record = {
            month: month,
            fileName: req.file.originalname,
            uploadDate: new Date()
          };

          // Map row data to sanitized column names
          headers.forEach((header, index) => {
            if (header) {
              const columnName = DataModel.sanitizeColumnName(header);
              record[columnName] = row[index] || null;
            }
          });

          recordsToInsert.push(record);
        }
      });

      // Insert data in batches
      const batchSize = 1000;
      let totalInserted = 0;

      for (let i = 0; i < recordsToInsert.length; i += batchSize) {
        const batch = recordsToInsert.slice(i, i + batchSize);
        await model.bulkCreate(batch);
        totalInserted += batch.length;
      }

      // Update upload history
      await uploadRecord.update({
        recordCount: totalInserted,
        status: 'success'
      });

      // Clean up uploaded file
      fs.unlinkSync(req.file.path);

      res.json({
        success: true,
        message: 'File uploaded and processed successfully',
        recordsProcessed: totalInserted,
        month: month
      });

    } catch (processingError) {
      console.error('Error processing file:', processingError);
      
      // Update upload history with error
      await uploadRecord.update({
        status: 'failed',
        errorMessage: processingError.message
      });

      // Clean up uploaded file
      if (fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        message: 'Error processing file: ' + processingError.message
      });
    }

  } catch (error) {
    console.error('Upload error:', error);
    
    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Upload failed'
    });
  }
});

// Get upload history
router.get('/history', isAuthenticated, async (req, res) => {
  try {
    const history = await UploadHistory.findAll({
      order: [['uploadDate', 'DESC']],
      limit: 50
    });

    res.json({
      success: true,
      history: history
    });
  } catch (error) {
    console.error('Error fetching upload history:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching upload history'
    });
  }
});

module.exports = router;
