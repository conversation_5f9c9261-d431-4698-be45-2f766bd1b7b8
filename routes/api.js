const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const DataModel = require('../models/DataModel');
const UploadHistory = require('../models/UploadHistory');

// Get dashboard data with KPI and filtered data
router.get('/dashboard-data', isAuthenticated, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // Default to wider date range if no dates provided
    const now = new Date();
    const defaultStartDate = startDate || new Date(2025, 0, 1).toISOString().split('T')[0]; // Jan 1, 2025
    const defaultEndDate = endDate || new Date(2025, 11, 31).toISOString().split('T')[0]; // Dec 31, 2025

    // Get KPI data
    const kpi = await DataModel.getKPIData(defaultStartDate, defaultEndDate);
    
    // Get all data for the date range
    const data = await DataModel.getDataByDateRange(defaultStartDate, defaultEndDate);

    // Process data to ensure consistent format
    const processedData = data.map(record => {
      // Remove Sequelize metadata and flatten the record
      const cleanRecord = {};
      
      Object.keys(record).forEach(key => {
        if (key !== 'createdAt' && key !== 'updatedAt' && key !== 'id') {
          cleanRecord[key] = record[key];
        }
      });

      // Try to identify role from various possible field names
      const roleFields = ['role', 'designation', 'position', 'type', 'category'];
      let identifiedRole = null;

      for (const field of roleFields) {
        if (cleanRecord[field]) {
          const value = cleanRecord[field].toString().toUpperCase();
          if (value.includes('ZM')) identifiedRole = 'ZM';
          else if (value.includes('RM')) identifiedRole = 'RM';
          else if (value.includes('HQ')) identifiedRole = 'HQ';
          else if (value.includes('MR')) identifiedRole = 'MR';
          
          if (identifiedRole) {
            cleanRecord.role = identifiedRole;
            break;
          }
        }
      }

      return cleanRecord;
    });

    res.json({
      success: true,
      kpi: kpi,
      data: processedData,
      dateRange: {
        startDate: defaultStartDate,
        endDate: defaultEndDate
      },
      totalRecords: processedData.length
    });

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching dashboard data',
      error: error.message
    });
  }
});

// Get data filtered by role
router.get('/data-by-role', isAuthenticated, async (req, res) => {
  try {
    const { role, startDate, endDate } = req.query;
    
    if (!role) {
      return res.status(400).json({
        success: false,
        message: 'Role parameter is required'
      });
    }

    // Default to current month if no dates provided
    const now = new Date();
    const defaultStartDate = startDate || new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
    const defaultEndDate = endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];

    // Get all data for the date range
    const allData = await DataModel.getDataByDateRange(defaultStartDate, defaultEndDate);
    
    // Filter by role
    const filteredData = allData.filter(record => {
      const roleFields = ['role', 'designation', 'position', 'type', 'category'];
      
      for (const field of roleFields) {
        if (record[field]) {
          const value = record[field].toString().toUpperCase();
          if (value.includes(role.toUpperCase())) {
            return true;
          }
        }
      }
      return false;
    });

    res.json({
      success: true,
      data: filteredData,
      role: role,
      totalRecords: filteredData.length
    });

  } catch (error) {
    console.error('Error fetching role data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching role data',
      error: error.message
    });
  }
});

// Get available months
router.get('/available-months', isAuthenticated, async (req, res) => {
  try {
    const months = await DataModel.getAvailableMonths();
    
    res.json({
      success: true,
      months: months
    });

  } catch (error) {
    console.error('Error fetching available months:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching available months',
      error: error.message
    });
  }
});

// Get upload history
router.get('/upload-history', isAuthenticated, async (req, res) => {
  try {
    const history = await UploadHistory.findAll({
      where: {
        status: 'success'
      },
      order: [['uploadDate', 'DESC']],
      limit: 20,
      attributes: ['month', 'originalName', 'recordCount', 'uploadDate']
    });

    const formattedHistory = history.map(record => ({
      month: record.month,
      fileName: record.originalName,
      recordCount: record.recordCount,
      uploadDate: record.uploadDate
    }));

    res.json({
      success: true,
      history: formattedHistory
    });

  } catch (error) {
    console.error('Error fetching upload history:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching upload history',
      error: error.message
    });
  }
});

// Get summary statistics
router.get('/summary', isAuthenticated, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // Default to current month if no dates provided
    const now = new Date();
    const defaultStartDate = startDate || new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
    const defaultEndDate = endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];

    // Get all data
    const data = await DataModel.getDataByDateRange(defaultStartDate, defaultEndDate);
    const kpi = await DataModel.getKPIData(defaultStartDate, defaultEndDate);
    const availableMonths = await DataModel.getAvailableMonths();

    res.json({
      success: true,
      summary: {
        totalRecords: data.length,
        kpi: kpi,
        availableMonths: availableMonths.length,
        dateRange: {
          startDate: defaultStartDate,
          endDate: defaultEndDate
        }
      }
    });

  } catch (error) {
    console.error('Error fetching summary:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching summary',
      error: error.message
    });
  }
});

module.exports = router;
