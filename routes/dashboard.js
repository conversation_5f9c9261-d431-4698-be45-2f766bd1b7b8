const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');

// Redirect root to login if not authenticated, dashboard if authenticated
router.get('/', (req, res) => {
  if (req.session && req.session.user) {
    res.redirect('/megacare/hqprd/dashboard');
  } else {
    res.redirect('/megacare/hqprd/login');
  }
});

// Login page route - GET only for displaying form
router.get('/login', (req, res) => {
  if (req.session && req.session.user) {
    res.redirect('/megacare/hqprd/dashboard');
  } else {
    res.sendFile('login.html', { root: './public' });
  }
});

// Login form submission - POST only for security
router.post('/login', (req, res) => {
  const { username, password } = req.body;

  // Check credentials
  if (username === process.env.ADMIN_USERNAME && password === process.env.ADMIN_PASSWORD) {
    req.session.user = {
      username: username,
      loginTime: new Date()
    };
    res.redirect('/megacare/hqprd/dashboard');
  } else {
    res.redirect('/megacare/hqprd/login?error=invalid');
  }
});

// Dashboard page
router.get('/dashboard', isAuthenticated, (req, res) => {
  res.sendFile('dashboard.html', { root: './public' });
});

// Upload page
router.get('/upload', isAuthenticated, (req, res) => {
  res.sendFile('upload.html', { root: './public' });
});

// Logout
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({ success: false, message: 'Could not log out' });
    }
    res.redirect('/megacare/hqprd/login');
  });
});

module.exports = router;
