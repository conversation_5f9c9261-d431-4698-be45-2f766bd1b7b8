// Simple test script to verify the application endpoints
const http = require('http');

function testEndpoint(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

async function runTests() {
  console.log('Testing Megacare HQ PRD Application...\n');

  try {
    // Test root redirect
    console.log('1. Testing root redirect...');
    const rootTest = await testEndpoint('/');
    console.log(`   Status: ${rootTest.statusCode} (Expected: 302 redirect)`);

    // Test login page
    console.log('2. Testing login page...');
    const loginTest = await testEndpoint('/megacare/hqprd/login');
    console.log(`   Status: ${loginTest.statusCode} (Expected: 200)`);

    // Test dashboard without auth (should redirect)
    console.log('3. Testing dashboard without auth...');
    const dashboardTest = await testEndpoint('/megacare/hqprd/dashboard');
    console.log(`   Status: ${dashboardTest.statusCode} (Expected: 302 redirect to login)`);

    // Test upload without auth (should redirect)
    console.log('4. Testing upload without auth...');
    const uploadTest = await testEndpoint('/megacare/hqprd/upload');
    console.log(`   Status: ${uploadTest.statusCode} (Expected: 302 redirect to login)`);

    // Test static assets
    console.log('5. Testing CSS assets...');
    const cssTest = await testEndpoint('/megacare/hqprd/assets/css/bootstrap.min.css');
    console.log(`   Status: ${cssTest.statusCode} (Expected: 200)`);

    console.log('\n✅ Basic endpoint tests completed!');
    console.log('\nTo test the full application:');
    console.log('1. Open http://localhost:3000/megacare/hqprd in your browser');
    console.log('2. Login with username: admin, password: megacare');
    console.log('3. Test file upload and dashboard functionality');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

runTests();
