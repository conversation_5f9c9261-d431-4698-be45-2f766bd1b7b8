{"name": "megacare-hq-prd", "version": "1.0.0", "description": "Megacare HQ PRD Dashboard Application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["megacare", "dashboard", "excel", "upload"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2"}}