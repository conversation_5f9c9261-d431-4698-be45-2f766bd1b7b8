// Test script to verify CSV upload functionality
const FormData = require('form-data');
const fs = require('fs');
const http = require('http');

async function testCSVUpload() {
  console.log('Testing CSV upload functionality...\n');

  // Create a test CSV file
  const csvContent = `Name,Role,Department,Location,Salary
<PERSON>,ZM,Sales,Mumbai,50000
Jane Smith,RM,Marketing,Delhi,45000
Bob Johnson,HQ,Operations,Bangalore,60000`;

  fs.writeFileSync('test-upload.csv', csvContent);
  console.log('✅ Test CSV file created');

  // Test file upload
  const form = new FormData();
  form.append('file', fs.createReadStream('test-upload.csv'));
  form.append('month', '2025-01');

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/megacare/hqprd/upload/file',
    method: 'POST',
    headers: {
      ...form.getHeaders(),
      'Cookie': 'connect.sid=test-session' // You'll need a valid session
    }
  };

  console.log('📤 Testing file upload...');
  console.log('Note: This test requires a valid login session');
  console.log('Please test manually by:');
  console.log('1. Login to the application');
  console.log('2. Go to upload page');
  console.log('3. Try uploading the test-upload.csv file');
  console.log('4. Check if browse button works by clicking the upload area');

  // Clean up
  fs.unlinkSync('test-upload.csv');
  console.log('✅ Test file cleaned up');
}

testCSVUpload();
